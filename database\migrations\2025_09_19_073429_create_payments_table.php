<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id('PaymentId');
            
            // Either Sale or Purchase (nullable one of them)
            $table->unsignedBigInteger('SaleID')->nullable();
            $table->unsignedBigInteger('PurchaseID')->nullable();

            $table->enum('PaymentType', ['Customer', 'Supplier']); 
            $table->enum('PaymentMethod', ['Cash', 'Bank', 'Credit Card']); 
            $table->decimal('Amount', 10, 2);
            $table->date('PaymentDate');

            $table->timestamps();

            // Foreign Keys
            $table->foreign('SaleID')->references('sale_id')->on('sale_orders')->onDelete('cascade');
            $table->foreign('PurchaseID')->references('id')->on('purchase_orders')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
